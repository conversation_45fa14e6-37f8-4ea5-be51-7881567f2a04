import os
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

import oss2

from app.core.config import settings


def get_oss_url(object_name: str, expire_seconds: int = 3600) -> str:
    """
    获取OSS文件的签名URL

    Args:
        object_name: OSS中的文件路径
        expire_seconds: 签名有效期，单位秒，默认1小时

    Returns:
        签名后的URL
    """
    # 如果object_name为空或None，直接返回空字符串
    if not object_name:
        return ""

    # 如果object_name已经是完整URL，直接返回
    if object_name.startswith('http'):
        return object_name

    # 初始化OSS客户端
    auth = oss2.Auth(settings.OSS_ACCESS_KEY_ID, settings.OSS_ACCESS_KEY_SECRET)
    bucket = oss2.Bucket(auth, settings.OSS_ENDPOINT, settings.OSS_BUCKET)

    # 生成带签名的URL
    url = bucket.sign_url('GET', object_name, expire_seconds)
    return url


def generate_oss_signature(
        file_path: str,
        content_type:
        Optional[str] = None,
        expire_seconds: int = 300) -> Dict[str, Any]:
    """
    生成OSS直传签名

    Args:
        file_path: 文件路径，相对于bucket根目录
        content_type: 文件内容类型，如image/jpeg
        expire_seconds: 签名有效期，单位秒

    Returns:
        包含签名信息的字典
    """
    # 初始化OSS客户端
    auth = oss2.Auth(settings.OSS_ACCESS_KEY_ID, settings.OSS_ACCESS_KEY_SECRET)
    bucket = oss2.Bucket(auth, settings.OSS_ENDPOINT, settings.OSS_BUCKET)

    # 计算过期时间
    expire_time = int((datetime.now() + timedelta(seconds=expire_seconds)).timestamp())

    # 生成签名URL
    params = {
        'expires': expire_seconds
    }

    headers = {}
    if content_type:
        headers['Content-Type'] = content_type

    # 生成PUT签名URL
    url = bucket.sign_url('PUT', file_path, expire_seconds, headers=headers)

    return {
        "upload_url": url,
        "file_path": file_path,
        "file_url": f"{settings.OSS_BASE_URL}/{file_path}",
        "headers": headers,
        "expires": expire_time
    }


def generate_avatar_upload_url(file_extension: str) -> Dict[str, Any]:
    """
    生成头像上传的签名URL

    Args:
        file_extension: 文件扩展名，如jpg, png等

    Returns:
        包含签名信息的字典
    """
    # 生成唯一文件名
    file_name = f"{uuid.uuid4()}.{file_extension}"

    # 构建文件路径，放在avatar目录下
    file_path = f"assistant/avatar/{file_name}"

    # 根据扩展名确定内容类型
    content_type_map = {
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif',
        'webp': 'image/webp'
    }

    content_type = content_type_map.get(file_extension.lower(), 'application/octet-stream')

    # 生成签名
    return generate_oss_signature(file_path, content_type)


def get_file_extension(filename: str) -> str:
    """
    从文件名中获取扩展名

    Args:
        filename: 文件名

    Returns:
        文件扩展名，不包含点号
    """
    _, ext = os.path.splitext(filename)
    if ext.startswith('.'):
        ext = ext[1:]
    return ext
