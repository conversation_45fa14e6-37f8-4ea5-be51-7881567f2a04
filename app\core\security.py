from datetime import datetime, timedelta, UTC
from typing import Any, Union

from jose import jwt

from app.core.config import settings


def create_access_token(
        subject: Union[str, Any], token_version: int, expires_delta: timedelta = None
) -> str:
    if expires_delta:
        expire = datetime.now(UTC) + expires_delta
    else:
        expire = datetime.now(UTC) + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    to_encode = {
        "exp": expire,
        "sub": str(subject),
        "ver": token_version
    }
    encoded_jwt = jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM
    )
    return encoded_jwt


