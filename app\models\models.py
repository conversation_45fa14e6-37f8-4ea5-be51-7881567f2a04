from sqlalchemy import Column, Foreign<PERSON><PERSON>, Integer, String, Text, DateTime, func
from sqlalchemy.orm import relationship

from app.db.base_class import Base


# System Tables

class SysAdmin(Base):
    """系统管理员表"""
    __tablename__ = "sys_admin"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="系统管理员ID")
    uid = Column(Integer, ForeignKey("sys_user.id"), nullable=False, unique=True, comment="用户ID")
    name = Column(String(255), nullable=False, comment="姓名")
    role = Column(Integer, nullable=False, default=1, comment="角色（0：超级管理员；1：管理员）")

    # Relationships
    user = relationship("SysUser", back_populates="admin")


class SysBconf(Base):
    """全局机器人设置表"""
    __tablename__ = "sys_bconf"

    key = Column(String(45), primary_key=True, comment="AI设置key")
    bid = Column(Integer, ForeignKey("sys_bot.id"), nullable=False, comment="机器人ID")
    notes = Column(String(512), comment="备注")

    # Relationships
    bot = relationship("SysBot", back_populates="global_configs")


class SysBot(Base):
    """机器人表"""
    __tablename__ = "sys_bot"

    id = Column(Integer, primary_key=True, comment="机器人ID")
    name = Column(String(255), nullable=False, comment="名称")
    api_endpoint = Column(String(512), comment="API 路径")
    api_key = Column(String(512), comment="API key")
    sys_prompt = Column(Text, comment="系统提示词模板")
    notes = Column(String(512), comment="备注")
    active = Column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    global_configs = relationship("SysBconf", back_populates="bot")
    tenant_configs = relationship("TntBconf", back_populates="bot")
    characters = relationship("TntCharacter", back_populates="bot")


class SysTenant(Base):
    """租户表"""
    __tablename__ = "sys_tenant"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="租户ID")
    code = Column(String(45), nullable=False, unique=True, comment="租户代号")
    name = Column(String(255), comment="名称")
    notes = Column(String(512), comment="备注")
    ctime = Column(DateTime, nullable=False, default=func.current_timestamp(), comment="创建时间")
    active = Column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    admins = relationship("TntAdmin", back_populates="tenant")
    bot_configs = relationship("TntBconf", back_populates="tenant")
    characters = relationship("TntCharacter", back_populates="tenant")
    classes = relationship("TntClass", back_populates="tenant")
    cues = relationship("TntCue", back_populates="tenant")
    eduops = relationship("TntEduops", back_populates="tenant")
    exercises = relationship("TntExercise", back_populates="tenant")
    exercise_logs = relationship("TntExerciseLog", back_populates="tenant")
    frameworks = relationship("TntFramework", back_populates="tenant")
    lines = relationship("TntLine", back_populates="tenant")
    modules = relationship("TntModule", back_populates="tenant")
    plans = relationship("TntPlan", back_populates="tenant")
    questions = relationship("TntQuestion", back_populates="tenant")
    scenes = relationship("TntScene", back_populates="tenant")
    scene_characters = relationship("TntSceneCharacter", back_populates="tenant")
    scene_guides = relationship("TntSceneGuide", back_populates="tenant")
    scene_speeches = relationship("TntSceneSpeech", back_populates="tenant")
    students = relationship("TntStudent", back_populates="tenant")
    subjects = relationship("TntSubject", back_populates="tenant")
    teachers = relationship("TntTeacher", back_populates="tenant")
    units = relationship("TntUnit", back_populates="tenant")
    worksheets = relationship("TntWorksheet", back_populates="tenant")
    worksheet_answers = relationship("TntWorksheetAnswer", back_populates="tenant")


class SysUser(Base):
    """用户表"""
    __tablename__ = "sys_user"

    id = Column(Integer, primary_key=True, comment="用户ID")
    username = Column(String(45), nullable=False, unique=True, comment="用户名")
    passwd = Column(String(255), nullable=False, comment="密码")
    token_version = Column(Integer, nullable=False, default=0, comment="token版本，用于登出")
    ctime = Column(DateTime, nullable=False, default=func.current_timestamp(), comment="创建时间")
    active = Column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    admin = relationship("SysAdmin", back_populates="user", uselist=False)
    tenant_admins = relationship("TntAdmin", back_populates="user")
    eduops = relationship("TntEduops", back_populates="user")
    students = relationship("TntStudent", back_populates="user")


# Tenant Tables

class TntAdmin(Base):
    """租户管理员表"""
    __tablename__ = "tnt_admin"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="租户管理员ID")
    uid = Column(Integer, ForeignKey("sys_user.id"), nullable=False, comment="用户ID")
    name = Column(String(255), nullable=False, comment="姓名")
    role = Column(Integer, nullable=False, default=1, comment="角色（1：管理员）")

    # Relationships
    tenant = relationship("SysTenant", back_populates="admins")
    user = relationship("SysUser", back_populates="tenant_admins")


class TntBconf(Base):
    """租户机器人设置表"""
    __tablename__ = "tnt_bconf"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    key = Column(String(45), primary_key=True, comment="AI设置key")
    bid = Column(Integer, ForeignKey("sys_bot.id"), nullable=False, comment="机器人ID")
    notes = Column(String(512), comment="备注")

    # Relationships
    tenant = relationship("SysTenant", back_populates="bot_configs")
    bot = relationship("SysBot", back_populates="tenant_configs")


class TntCharacter(Base):
    """人物表"""
    __tablename__ = "tnt_character"

    tenant_id = Column(Integer, ForeignKey("sys_bot.id"), nullable=False, comment="租户ID")  # Note: FK references sys_bot in original schema
    id = Column(Integer, primary_key=True, autoincrement=True, comment="人物ID")
    name = Column(String(255), nullable=False, comment="姓名")
    gender = Column(Integer, nullable=False, default=0, comment="性别（0：未知；1：男；2：女；）")
    avatar = Column(String(255), nullable=False, comment="头像URL")
    profile = Column(Text, nullable=False)
    timbre_type = Column(Integer, nullable=False, default=0, comment="音色类型（0：未设置；1：火山引擎；）")
    timbre = Column(String(255), comment="语音音色")
    notes = Column(String(512), comment="备注")
    pv_profile = Column(Text, comment="人物资料（提示词变量，默认为profile，后台用）")
    pv_ability = Column(Text, comment="人物能力（提示词变量，后台用）")
    pv_restriction = Column(Text, comment="人物限制（提示词变量，后台用）")
    published = Column(Integer, nullable=False, default=0, comment="是否发布（0：未发布；1：已发布）")
    ctime = Column(DateTime, nullable=False, default=func.current_timestamp(), comment="创建时间")
    active = Column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    bot = relationship("SysBot", back_populates="characters")  # Note: This should be tenant in a corrected schema
    tenant = relationship("SysTenant", back_populates="characters", foreign_keys=[tenant_id])
    cues = relationship("TntCue", back_populates="character")
    lines = relationship("TntLine", back_populates="character")
    scene_characters = relationship("TntSceneCharacter", back_populates="character")
    scene_speeches = relationship("TntSceneSpeech", back_populates="character")


class TntClass(Base):
    """班级表"""
    __tablename__ = "tnt_class"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="班级ID")
    name = Column(String(255), nullable=False, comment="名称")
    pic = Column(String(255), comment="图片URL")
    description = Column(Text, comment="描述")
    notes = Column(String(512), comment="备注")
    btime = Column(DateTime, nullable=False, default=func.current_timestamp(), comment="开始时间")
    etime = Column(DateTime, nullable=False, default=func.current_timestamp(), comment="结束时间")
    ctime = Column(DateTime, nullable=False, default=func.current_timestamp(), comment="创建时间")
    active = Column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant = relationship("SysTenant", back_populates="classes")
    class_exercises = relationship("TntClassExercise", back_populates="class_")
    class_students = relationship("TntClassStudent", back_populates="class_")
    exercise_logs = relationship("TntExerciseLog", back_populates="class_")
    eduops_classes = relationship("TntEduopsClass", back_populates="class_")


class TntClassExercise(Base):
    """班级练习关系表"""
    __tablename__ = "tnt_class_exercise"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    cid = Column(Integer, ForeignKey("tnt_class.id"), nullable=False, comment="班级ID")
    eid = Column(Integer, ForeignKey("tnt_exercise.id"), nullable=False, comment="练习ID")
    tid = Column(Integer, ForeignKey("tnt_teacher.id"), nullable=False, comment="老师ID")
    depend = Column(Integer, nullable=False, default=1, comment="是否依赖前一个练习结束（0：不依赖；1：依赖）")
    priority = Column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")
    active = Column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant = relationship("SysTenant")
    class_ = relationship("TntClass", back_populates="class_exercises")
    exercise = relationship("TntExercise", back_populates="class_exercises")
    teacher = relationship("TntTeacher", back_populates="class_exercises")


class TntClassStudent(Base):
    """班级学员关系表"""
    __tablename__ = "tnt_class_student"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    cid = Column(Integer, ForeignKey("tnt_class.id"), nullable=False, comment="班级ID")
    sid = Column(Integer, ForeignKey("tnt_student.id"), nullable=False, comment="学员ID")
    priority = Column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")
    active = Column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant = relationship("SysTenant")
    class_ = relationship("TntClass", back_populates="class_students")
    student = relationship("TntStudent", back_populates="class_students")


class TntCue(Base):
    """剧本提示表"""
    __tablename__ = "tnt_cue"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="剧本提示ID")
    sid = Column(Integer, ForeignKey("tnt_scene.id"), nullable=False, comment="场景ID")
    cid = Column(Integer, ForeignKey("tnt_character.id"), nullable=False, comment="人物ID（触发人物）")
    content = Column(String(1024), nullable=False, comment="触发内容（当这个人物发言为此内容时触发）")
    serial = Column(Integer, nullable=False, default=1, comment="是否顺序发言（0：并行发言；1：顺序发言）")
    priority = Column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")
    active = Column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant = relationship("SysTenant", back_populates="cues")
    scene = relationship("TntScene", back_populates="cues")
    character = relationship("TntCharacter", back_populates="cues")
    lines = relationship("TntLine", back_populates="cue")


class TntEduops(Base):
    """教学运营人员表"""
    __tablename__ = "tnt_eduops"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="教学运营人员ID")
    uid = Column(Integer, ForeignKey("sys_user.id"), nullable=False, comment="用户ID")
    name = Column(String(255), nullable=False, comment="姓名")
    gender = Column(Integer, nullable=False, default=0, comment="性别（0：未知；1：男；2：女；）")
    notes = Column(String(512), comment="备注")

    # Relationships
    tenant = relationship("SysTenant", back_populates="eduops")
    user = relationship("SysUser", back_populates="eduops")
    eduops_classes = relationship("TntEduopsClass", back_populates="eduops")


class TntEduopsClass(Base):
    """教学跟踪关系表"""
    __tablename__ = "tnt_eduops_class"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    eoid = Column(Integer, ForeignKey("tnt_eduops.id"), nullable=False, comment="教学运营人员ID")
    cid = Column(Integer, ForeignKey("tnt_class.id"), nullable=False, comment="班级ID")

    # Relationships
    tenant = relationship("SysTenant")
    eduops = relationship("TntEduops", back_populates="eduops_classes")
    class_ = relationship("TntClass", back_populates="eduops_classes")


class TntExercise(Base):
    """练习表"""
    __tablename__ = "tnt_exercise"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="练习ID")
    title = Column(String(255), nullable=False, comment="标题")
    type = Column(Integer, nullable=False, default=1, comment="类型（1：作业单；2：角色扮演；）")
    intro = Column(String(255), comment="简介")
    duration = Column(Integer, comment="预估练习时长（分钟）")
    version = Column(String(255), comment="版本")
    bgtext = Column(Text, comment="背景文字")
    bgvideo = Column(String(255), comment="背景视频URL")
    notes = Column(String(512), comment="备注")
    published = Column(Integer, nullable=False, default=0, comment="是否发布（0：未发布；1：已发布）")
    ctime = Column(DateTime, nullable=False, default=func.current_timestamp(), comment="创建时间")
    active = Column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant = relationship("SysTenant", back_populates="exercises")
    class_exercises = relationship("TntClassExercise", back_populates="exercise")
    exercise_logs = relationship("TntExerciseLog", back_populates="exercise")
    plan_exercises = relationship("TntPlanExercise", back_populates="exercise")
    scenes = relationship("TntScene", back_populates="exercise")
    worksheets = relationship("TntWorksheet", back_populates="exercise")


class TntExerciseLog(Base):
    """练习情况表"""
    __tablename__ = "tnt_exercise_log"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="练习情况ID")
    cid = Column(Integer, ForeignKey("tnt_class.id"), nullable=False, comment="班级ID")
    sid = Column(Integer, ForeignKey("tnt_student.id"), nullable=False, comment="学员ID")
    eid = Column(Integer, ForeignKey("tnt_exercise.id"), nullable=False, comment="练习ID")
    report = Column(String(255), comment="整体点评报告URL")
    btime = Column(DateTime, nullable=False, default=func.current_timestamp(), comment="开始时间")
    etime = Column(DateTime, nullable=False, default=func.current_timestamp(), comment="结束时间")
    utime = Column(DateTime, nullable=False, default=func.current_timestamp(), comment="上次更新时间")
    active = Column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant = relationship("SysTenant", back_populates="exercise_logs")
    class_ = relationship("TntClass", back_populates="exercise_logs")
    student = relationship("TntStudent", back_populates="exercise_logs")
    exercise = relationship("TntExercise", back_populates="exercise_logs")
    scene_speeches = relationship("TntSceneSpeech", back_populates="exercise_log")
    worksheet_answers = relationship("TntWorksheetAnswer", back_populates="exercise_log")


class TntFramework(Base):
    """理论框架表"""
    __tablename__ = "tnt_framework"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="理论框架ID")
    name = Column(String(255), nullable=False, comment="理论框架名称（如：PMI人才三角等）")
    description = Column(Text, comment="详细描述")
    logo = Column(String(255), comment="Logo URL")
    priority = Column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")
    active = Column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant = relationship("SysTenant", back_populates="frameworks")
    modules = relationship("TntModule", back_populates="framework")


class TntLine(Base):
    """台词表"""
    __tablename__ = "tnt_line"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="台词ID")
    cueid = Column(Integer, ForeignKey("tnt_cue.id"), nullable=False, comment="剧本提示ID")
    cid = Column(Integer, ForeignKey("tnt_character.id"), nullable=False, comment="人物ID（发言人物）")
    pv_topic = Column(String(512), nullable=False, comment="发言主题（提示词变量，后台用）")
    pv_ability = Column(Text, nullable=False, comment="人物能力（提示词变量，后台用）")
    pv_restriction = Column(Text, nullable=False, comment="人物限制（提示词变量，后台用）")
    priority = Column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")
    active = Column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant = relationship("SysTenant", back_populates="lines")
    cue = relationship("TntCue", back_populates="lines")
    character = relationship("TntCharacter", back_populates="lines")


class TntModule(Base):
    """理论模块表"""
    __tablename__ = "tnt_module"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="理论模块ID")
    fid = Column(Integer, ForeignKey("tnt_framework.id"), nullable=False, comment="理论框架ID")
    name = Column(String(255), nullable=False, comment="理论模块名称")
    description = Column(Text, comment="详细描述")
    priority = Column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")
    active = Column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant = relationship("SysTenant", back_populates="modules")
    framework = relationship("TntFramework", back_populates="modules")
    question_modules = relationship("TntQuestionModule", back_populates="module")


class TntPlan(Base):
    """计划表"""
    __tablename__ = "tnt_plan"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="计划ID")
    name = Column(String(255), nullable=False, comment="名称")
    pic = Column(String(255), comment="图片URL")
    description = Column(Text, comment="描述")
    notes = Column(String(512), comment="备注")
    active = Column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant = relationship("SysTenant", back_populates="plans")
    plan_exercises = relationship("TntPlanExercise", back_populates="plan")


class TntPlanExercise(Base):
    """计划练习关系表"""
    __tablename__ = "tnt_plan_exercise"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    pid = Column(Integer, ForeignKey("tnt_plan.id"), nullable=False, comment="计划ID")
    eid = Column(Integer, ForeignKey("tnt_exercise.id"), nullable=False, comment="练习ID")
    depend = Column(Integer, nullable=False, default=1, comment="是否依赖前一个练习结束（0：不依赖；1：依赖）")
    priority = Column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")

    # Relationships
    tenant = relationship("SysTenant")
    plan = relationship("TntPlan", back_populates="plan_exercises")
    exercise = relationship("TntExercise", back_populates="plan_exercises")


class TntQuestion(Base):
    """问题表"""
    __tablename__ = "tnt_question"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="问题ID")
    title = Column(String(255), nullable=False, comment="标题")
    bgtext = Column(Text, comment="背景文字")
    bgvideo = Column(String(255), comment="背景视频URL")
    notes = Column(String(512), comment="备注")
    pv_skills = Column(Text, comment="点评能力（提示词变量，后台用）")
    pv_rules = Column(Text, comment="点评细则（提示词变量，后台用）")
    pv_formats = Column(Text, comment="点评格式（提示词变量，后台用）")
    ctime = Column(DateTime, nullable=False, default=func.current_timestamp(), comment="创建时间")
    active = Column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant = relationship("SysTenant", back_populates="questions")
    question_guides = relationship("TntQuestionGuide", back_populates="question")
    question_modules = relationship("TntQuestionModule", back_populates="question")
    question_subjects = relationship("TntQuestionSubject", back_populates="question")
    worksheet_answers = relationship("TntWorksheetAnswer", back_populates="question")
    worksheet_orgs = relationship("TntWorksheetOrg", back_populates="question")


class TntQuestionGuide(Base):
    """问题作答指南表"""
    __tablename__ = "tnt_question_guide"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="作答指南ID")
    qid = Column(Integer, ForeignKey("tnt_question.id"), nullable=False, comment="问题ID")
    title = Column(String(255), nullable=False, comment="指南标题")
    details = Column(Text, nullable=False, comment="指南详情")
    priority = Column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")

    # Relationships
    tenant = relationship("SysTenant")
    question = relationship("TntQuestion", back_populates="question_guides")


class TntQuestionModule(Base):
    """问题-理论模块关系表"""
    __tablename__ = "tnt_question_module"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    qid = Column(Integer, ForeignKey("tnt_question.id"), nullable=False, comment="问题ID")
    mid = Column(Integer, ForeignKey("tnt_module.id"), nullable=False, comment="理论模块ID")

    # Relationships
    tenant = relationship("SysTenant")
    question = relationship("TntQuestion", back_populates="question_modules")
    module = relationship("TntModule", back_populates="question_modules")


class TntQuestionSubject(Base):
    """问题-主题关系表"""
    __tablename__ = "tnt_question_subject"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    qid = Column(Integer, ForeignKey("tnt_question.id"), nullable=False, comment="问题ID")
    sid = Column(Integer, ForeignKey("tnt_subject.id"), nullable=False, comment="主题ID")

    # Relationships
    tenant = relationship("SysTenant")
    question = relationship("TntQuestion", back_populates="question_subjects")
    subject = relationship("TntSubject", back_populates="question_subjects")


class TntScene(Base):
    """场景表"""
    __tablename__ = "tnt_scene"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="场景ID")
    eid = Column(Integer, ForeignKey("tnt_exercise.id"), nullable=False, comment="练习ID")
    pv_scripts = Column(Text, nullable=False, comment="剧情脚本（提示词变量，后台用）")

    # Relationships
    tenant = relationship("SysTenant", back_populates="scenes")
    exercise = relationship("TntExercise", back_populates="scenes")
    cues = relationship("TntCue", back_populates="scene")
    scene_characters = relationship("TntSceneCharacter", back_populates="scene")
    scene_guides = relationship("TntSceneGuide", back_populates="scene")


class TntSceneCharacter(Base):
    """场景人物关系表"""
    __tablename__ = "tnt_scene_character"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    sid = Column(Integer, ForeignKey("tnt_scene.id"), nullable=False, comment="场景ID")
    cid = Column(Integer, ForeignKey("tnt_character.id"), nullable=False, comment="人物ID")
    played = Column(Integer, nullable=False, default=0, comment="是否为学员扮演（0：否；1：是）")
    priority = Column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")

    # Relationships
    tenant = relationship("SysTenant", back_populates="scene_characters")
    scene = relationship("TntScene", back_populates="scene_characters")
    character = relationship("TntCharacter", back_populates="scene_characters")


class TntSceneGuide(Base):
    """场景练习指南表"""
    __tablename__ = "tnt_scene_guide"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="指南ID")
    sid = Column(Integer, ForeignKey("tnt_scene.id"), nullable=False, comment="场景ID")
    title = Column(String(255), nullable=False, comment="指南标题")
    details = Column(Text, nullable=False, comment="指南详情")
    priority = Column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")

    # Relationships
    tenant = relationship("SysTenant", back_populates="scene_guides")
    scene = relationship("TntScene", back_populates="scene_guides")


class TntSceneSpeech(Base):
    """场景练习情况表"""
    __tablename__ = "tnt_scene_speech"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="场景练习情况ID")
    elid = Column(Integer, ForeignKey("tnt_exercise_log.id"), nullable=False, comment="练习情况ID")
    cid = Column(Integer, ForeignKey("tnt_character.id"), nullable=False, comment="角色ID")
    played = Column(Integer, nullable=False, default=0, comment="是否是学员扮演（0：否；1：是）")
    content = Column(Text, nullable=False, comment="发言内容")
    to_cids = Column(String(255), comment="@列表（人物ID列表，用逗号分隔，可为空）")
    ctime = Column(DateTime, nullable=False, default=func.current_timestamp(), comment="发言时间")

    # Relationships
    tenant = relationship("SysTenant", back_populates="scene_speeches")
    exercise_log = relationship("TntExerciseLog", back_populates="scene_speeches")
    character = relationship("TntCharacter", back_populates="scene_speeches")


class TntStudent(Base):
    """学员表"""
    __tablename__ = "tnt_student"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="学员ID")
    uid = Column(Integer, ForeignKey("sys_user.id"), nullable=False, comment="用户ID")
    name = Column(String(255), nullable=False, comment="姓名")
    gender = Column(Integer, nullable=False, default=0, comment="性别（0：未知；1：男；2：女；）")
    notes = Column(String(512), comment="备注")

    # Relationships
    tenant = relationship("SysTenant", back_populates="students")
    user = relationship("SysUser", back_populates="students")
    class_students = relationship("TntClassStudent", back_populates="student")
    exercise_logs = relationship("TntExerciseLog", back_populates="student")


class TntSubject(Base):
    """主题表"""
    __tablename__ = "tnt_subject"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主题ID")
    name = Column(String(255), nullable=False, comment="主题名称（如：项目管理、领导力、沟通技巧等）")

    # Relationships
    tenant = relationship("SysTenant", back_populates="subjects")
    question_subjects = relationship("TntQuestionSubject", back_populates="subject")


class TntTeacher(Base):
    """老师表"""
    __tablename__ = "tnt_teacher"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="老师ID")
    name = Column(String(255), nullable=False, comment="姓名")
    gender = Column(Integer, nullable=False, default=0, comment="性别（0：未知；1：男；2：女；）")
    avatar = Column(String(255), nullable=False, comment="头像URL")
    intro = Column(Text, comment="简介")
    notes = Column(String(512), comment="备注")
    ctime = Column(DateTime, nullable=False, default=func.current_timestamp(), comment="创建时间")

    # Relationships
    tenant = relationship("SysTenant", back_populates="teachers")
    class_exercises = relationship("TntClassExercise", back_populates="teacher")


class TntUnit(Base):
    """单元模块表"""
    __tablename__ = "tnt_unit"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="单元模块ID")
    wid = Column(Integer, ForeignKey("tnt_worksheet.id"), nullable=False, comment="作业单ID")
    name = Column(String(255), nullable=False, comment="名称")
    bgtext = Column(Text, comment="背景文字")
    bgvideo = Column(String(255), comment="背景视频URL")
    priority = Column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")

    # Relationships
    tenant = relationship("SysTenant", back_populates="units")
    worksheet = relationship("TntWorksheet", back_populates="units")
    worksheet_orgs = relationship("TntWorksheetOrg", back_populates="unit")


class TntWorksheet(Base):
    """作业单表"""
    __tablename__ = "tnt_worksheet"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="作业单ID")
    eid = Column(Integer, ForeignKey("tnt_exercise.id"), nullable=False, comment="练习ID")

    # Relationships
    tenant = relationship("SysTenant", back_populates="worksheets")
    exercise = relationship("TntExercise", back_populates="worksheets")
    units = relationship("TntUnit", back_populates="worksheet")
    worksheet_orgs = relationship("TntWorksheetOrg", back_populates="worksheet")


class TntWorksheetAnswer(Base):
    """作业单作答表"""
    __tablename__ = "tnt_worksheet_answer"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="作业单作答ID")
    elid = Column(Integer, ForeignKey("tnt_exercise_log.id"), nullable=False, comment="练习情况ID")
    qid = Column(Integer, ForeignKey("tnt_question.id"), nullable=False, comment="问题ID")
    draft = Column(Text, nullable=False, comment="草稿")
    answer = Column(Text, nullable=False, comment="已提交作答")
    comment = Column(Text, nullable=False, comment="AI点评内容")
    stime = Column(DateTime, nullable=False, default=func.current_timestamp(), comment="提交时间")
    utime = Column(DateTime, nullable=False, default=func.current_timestamp(), comment="草稿更新时间")

    # Relationships
    tenant = relationship("SysTenant", back_populates="worksheet_answers")
    exercise_log = relationship("TntExerciseLog", back_populates="worksheet_answers")
    question = relationship("TntQuestion", back_populates="worksheet_answers")


class TntWorksheetAsm(Base):
    """作业单构成关系表"""
    __tablename__ = "tnt_worksheet_asm"

    tenant_id = Column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id = Column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    wid = Column(Integer, ForeignKey("tnt_worksheet.id"), nullable=False, comment="作业单ID")
    uid = Column(Integer, ForeignKey("tnt_unit.id"), nullable=False, comment="单元ID")
    qid = Column(Integer, ForeignKey("tnt_question.id"), nullable=False, comment="问题ID")
    priority = Column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")

    # Relationships
    tenant = relationship("SysTenant")
    worksheet = relationship("TntWorksheet", back_populates="worksheet_orgs")
    unit = relationship("TntUnit", back_populates="worksheet_orgs")
    question = relationship("TntQuestion", back_populates="worksheet_orgs")

