import logging

from fastapi import APIRouter

from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/health")
def health_check():
    """
    健康检查接口

    此接口用于检查系统的运行状态，返回系统的基本信息，包括节点名称、启动时间、构建版本和环境信息。
    可用于监控系统、负载均衡健康检查或确认系统是否正常运行。

    返回:
        dict: 包含系统状态信息的字典
            - nodeName: 项目名称
            - startTime: 系统启动时间
            - buildVersion: 系统版本号
            - env: 运行环境(如开发环境、测试环境、生产环境)
    """
    response = {
        "nodeName": settings.PROJECT_NAME,
        "startTime": settings.start_time,
        "buildVersion": settings.VERSION,
        "env": settings.ENVIRONMENT
    }
    logger.debug("Health check response: %s", response)
    return response
