-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: rm-uf6p2784e5r12h93l8o.mysql.rds.aliyuncs.com    Database: tmai
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ 'fcd1a3c2-cca2-11ef-8f7d-00163e341dae:1-880807';

--
-- Table structure for table `sys_admin`
--

DROP TABLE IF EXISTS `sys_admin`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_admin` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '系统管理员ID',
  `uid` int NOT NULL COMMENT '用户ID',
  `name` varchar(255) NOT NULL COMMENT '姓名',
  `role` int NOT NULL DEFAULT '1' COMMENT '角色（0：超级管理员；1：管理员）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`),
  UNIQUE KEY `uid_UNIQUE` (`uid`),
  CONSTRAINT `fk_admin_user` FOREIGN KEY (`uid`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统管理员表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_bconf`
--

DROP TABLE IF EXISTS `sys_bconf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_bconf` (
  `key` varchar(45) NOT NULL COMMENT 'AI设置key',
  `bid` int NOT NULL COMMENT '机器人ID',
  `notes` varchar(512) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`key`),
  UNIQUE KEY `key_UNIQUE` (`key`),
  KEY `fk_bconf_bot_idx` (`bid`),
  CONSTRAINT `fk_bconf_bot` FOREIGN KEY (`bid`) REFERENCES `sys_bot` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='全局机器人设置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_bot`
--

DROP TABLE IF EXISTS `sys_bot`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_bot` (
  `id` int NOT NULL COMMENT '机器人ID',
  `name` varchar(255) NOT NULL COMMENT '名称',
  `api_endpoint` varchar(512) DEFAULT NULL COMMENT 'API 路径',
  `api_key` varchar(512) DEFAULT NULL COMMENT 'API key',
  `sys_prompt` text COMMENT '系统提示词模板',
  `notes` varchar(512) DEFAULT NULL COMMENT '备注',
  `active` int NOT NULL DEFAULT '1' COMMENT '是否有效（0：失效；1：有效）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='机器人表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_tenant`
--

DROP TABLE IF EXISTS `sys_tenant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_tenant` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '租户ID',
  `code` varchar(45) NOT NULL COMMENT '租户代号',
  `name` varchar(255) DEFAULT NULL COMMENT '名称',
  `notes` varchar(512) DEFAULT NULL COMMENT '备注',
  `ctime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `active` int NOT NULL DEFAULT '1' COMMENT '是否有效（0：失效；1：有效）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`),
  UNIQUE KEY `code_UNIQUE` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='租户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_user`
--

DROP TABLE IF EXISTS `sys_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user` (
  `id` int NOT NULL COMMENT '用户ID',
  `username` varchar(45) NOT NULL COMMENT '用户名',
  `passwd` varchar(255) NOT NULL COMMENT '密码',
  `token_version` int NOT NULL DEFAULT '0' COMMENT 'token版本，用于登出',
  `ctime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `active` int NOT NULL DEFAULT '1' COMMENT '是否有效（0：失效；1：有效）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`),
  UNIQUE KEY `username_UNIQUE` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_admin`
--

DROP TABLE IF EXISTS `tnt_admin`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_admin` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '租户管理员ID',
  `uid` int NOT NULL COMMENT '用户ID',
  `name` varchar(255) NOT NULL COMMENT '姓名',
  `role` int NOT NULL DEFAULT '1' COMMENT '角色（1：管理员）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`),
  KEY `fk_tadmin_tenant_idx` (`tenant_id`),
  KEY `fk_tadmin_user_idx` (`uid`),
  CONSTRAINT `fk_tadmin_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_tadmin_user` FOREIGN KEY (`uid`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='租户管理员表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_bconf`
--

DROP TABLE IF EXISTS `tnt_bconf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_bconf` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `key` varchar(45) NOT NULL COMMENT 'AI设置key',
  `bid` int NOT NULL COMMENT '机器人ID',
  `notes` varchar(512) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`key`),
  KEY `fk_tbconf_tenant_idx` (`tenant_id`),
  KEY `fk_tbconf_bot_idx` (`bid`),
  CONSTRAINT `fk_tbconf_bot` FOREIGN KEY (`bid`) REFERENCES `sys_bot` (`id`),
  CONSTRAINT `fk_tbconf_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='租户机器人设置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_character`
--

DROP TABLE IF EXISTS `tnt_character`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_character` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '人物ID',
  `name` varchar(255) NOT NULL COMMENT '姓名',
  `gender` int NOT NULL DEFAULT '0' COMMENT '性别（0：未知；1：男；2：女；）',
  `avatar` varchar(255) NOT NULL COMMENT '头像URL',
  `profile` text NOT NULL,
  `timbre_type` int NOT NULL DEFAULT '0' COMMENT '音色类型（0：未设置；1：火山引擎；）',
  `timbre` varchar(255) DEFAULT NULL COMMENT '语音音色',
  `notes` varchar(512) DEFAULT NULL COMMENT '备注',
  `pv_profile` text COMMENT '人物资料（提示词变量，默认为profile，后台用）',
  `pv_ability` text COMMENT '人物能力（提示词变量，后台用）',
  `pv_restriction` text COMMENT '人物限制（提示词变量，后台用）',
  `published` int NOT NULL DEFAULT '0' COMMENT '是否发布（0：未发布；1：已发布）',
  `ctime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `active` int NOT NULL DEFAULT '1' COMMENT '是否有效（0：失效；1：有效）',
  PRIMARY KEY (`id`),
  KEY `fk_tcharacter_tenant_idx` (`tenant_id`),
  CONSTRAINT `fk_tcharacter_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_bot` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='人物表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_class`
--

DROP TABLE IF EXISTS `tnt_class`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_class` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '班级ID',
  `name` varchar(255) NOT NULL COMMENT '名称',
  `pic` varchar(255) DEFAULT NULL COMMENT '图片URL',
  `description` text COMMENT '描述',
  `notes` varchar(512) DEFAULT NULL COMMENT '备注',
  `btime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `etime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '结束时间',
  `ctime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `active` int NOT NULL DEFAULT '1' COMMENT '是否有效（0：失效；1：有效）',
  PRIMARY KEY (`id`),
  KEY `fk_tclass_tenant_idx` (`tenant_id`),
  CONSTRAINT `fk_tclass_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='班级表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_class_exercise`
--

DROP TABLE IF EXISTS `tnt_class_exercise`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_class_exercise` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `cid` int NOT NULL COMMENT '班级ID',
  `eid` int NOT NULL COMMENT '练习ID',
  `tid` int NOT NULL COMMENT '老师ID',
  `depend` int NOT NULL DEFAULT '1' COMMENT '是否依赖前一个练习结束（0：不依赖；1：依赖）',
  `priority` int NOT NULL DEFAULT '1' COMMENT '展示顺序（从小到大）',
  `active` int NOT NULL DEFAULT '1' COMMENT '是否有效（0：失效；1：有效）',
  PRIMARY KEY (`id`),
  KEY `fk_tce_tenant_idx` (`tenant_id`),
  KEY `fk_tce_tclass_idx` (`cid`),
  KEY `fk_tce_texercise_idx` (`eid`),
  KEY `fk_tce_tteacher_idx` (`tid`),
  CONSTRAINT `fk_tce_tclass` FOREIGN KEY (`cid`) REFERENCES `tnt_class` (`id`),
  CONSTRAINT `fk_tce_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_tce_texercise` FOREIGN KEY (`eid`) REFERENCES `tnt_exercise` (`id`),
  CONSTRAINT `fk_tce_tteacher` FOREIGN KEY (`tid`) REFERENCES `tnt_teacher` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='班级练习关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_class_student`
--

DROP TABLE IF EXISTS `tnt_class_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_class_student` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `cid` int NOT NULL COMMENT '班级ID',
  `sid` int NOT NULL COMMENT '学员ID',
  `priority` int NOT NULL DEFAULT '1' COMMENT '展示顺序（从小到大）',
  `active` int NOT NULL DEFAULT '1' COMMENT '是否有效（0：失效；1：有效）',
  PRIMARY KEY (`id`),
  KEY `fk_tcs_tenant_idx` (`tenant_id`),
  KEY `fk_tcs_tclass_idx` (`cid`),
  KEY `fk_tcs_tstudent_idx` (`sid`),
  CONSTRAINT `fk_tcs_tclass` FOREIGN KEY (`cid`) REFERENCES `tnt_class` (`id`),
  CONSTRAINT `fk_tcs_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_tcs_tstudent` FOREIGN KEY (`sid`) REFERENCES `tnt_student` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='班级学员关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_cue`
--

DROP TABLE IF EXISTS `tnt_cue`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_cue` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '剧本提示ID',
  `sid` int NOT NULL COMMENT '场景ID',
  `cid` int NOT NULL COMMENT '人物ID（触发人物）',
  `content` varchar(1024) NOT NULL COMMENT '触发内容（当这个人物发言为此内容时触发）',
  `serial` int NOT NULL DEFAULT '1' COMMENT '是否顺序发言（0：并行发言；1：顺序发言）',
  `priority` int NOT NULL DEFAULT '1' COMMENT '展示顺序（从小到大）',
  `active` int NOT NULL DEFAULT '1' COMMENT '是否有效（0：失效；1：有效）',
  PRIMARY KEY (`id`),
  KEY `fk_fcue_tenant_idx` (`tenant_id`),
  KEY `fk_fcue_tscene_idx` (`sid`),
  KEY `fk_fcue_tcharacter_idx` (`cid`),
  CONSTRAINT `fk_fcue_tcharacter` FOREIGN KEY (`cid`) REFERENCES `tnt_character` (`id`),
  CONSTRAINT `fk_fcue_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_fcue_tscene` FOREIGN KEY (`sid`) REFERENCES `tnt_scene` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='剧本提示表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_eduops`
--

DROP TABLE IF EXISTS `tnt_eduops`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_eduops` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '教学运营人员ID',
  `uid` int NOT NULL COMMENT '用户ID',
  `name` varchar(255) NOT NULL COMMENT '姓名',
  `gender` int NOT NULL DEFAULT '0' COMMENT '性别（0：未知；1：男；2：女；）',
  `notes` varchar(512) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `fk_teduops_tenant_idx` (`tenant_id`),
  KEY `fk_teduops_user_idx` (`uid`),
  CONSTRAINT `fk_teduops_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_teduops_user` FOREIGN KEY (`uid`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='教学运营人员表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_eduops_class`
--

DROP TABLE IF EXISTS `tnt_eduops_class`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_eduops_class` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `eoid` int NOT NULL COMMENT '教学运营人员ID',
  `cid` int NOT NULL COMMENT '班级ID',
  PRIMARY KEY (`id`),
  KEY `fk_tec_tenant_idx` (`tenant_id`),
  KEY `fk_tec_teduops_idx` (`eoid`),
  KEY `fk_tec_tclass_idx` (`cid`),
  CONSTRAINT `fk_tec_tclass` FOREIGN KEY (`cid`) REFERENCES `tnt_class` (`id`),
  CONSTRAINT `fk_tec_teduops` FOREIGN KEY (`eoid`) REFERENCES `tnt_eduops` (`id`),
  CONSTRAINT `fk_tec_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='教学跟踪关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_exercise`
--

DROP TABLE IF EXISTS `tnt_exercise`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_exercise` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '练习ID',
  `title` varchar(255) NOT NULL COMMENT '标题',
  `type` int NOT NULL DEFAULT '1' COMMENT '类型（1：作业单；2：角色扮演；）',
  `intro` varchar(255) DEFAULT NULL COMMENT '简介',
  `duration` int DEFAULT NULL COMMENT '预估练习时长（分钟）',
  `version` varchar(255) DEFAULT NULL COMMENT '版本',
  `bgtext` text COMMENT '背景文字',
  `bgvideo` varchar(255) DEFAULT NULL COMMENT '背景视频URL',
  `notes` varchar(512) DEFAULT NULL COMMENT '备注',
  `published` int NOT NULL DEFAULT '0' COMMENT '是否发布（0：未发布；1：已发布）',
  `ctime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `active` int NOT NULL DEFAULT '1' COMMENT '是否有效（0：失效；1：有效）',
  PRIMARY KEY (`id`),
  KEY `fk_texercise_tenant_idx` (`tenant_id`),
  CONSTRAINT `fk_texercise_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='练习表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_exercise_log`
--

DROP TABLE IF EXISTS `tnt_exercise_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_exercise_log` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '练习情况ID',
  `cid` int NOT NULL COMMENT '班级ID',
  `sid` int NOT NULL COMMENT '学员ID',
  `eid` int NOT NULL COMMENT '练习ID',
  `report` varchar(255) DEFAULT NULL COMMENT '整体点评报告URL',
  `btime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `etime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '结束时间',
  `utime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上次更新时间',
  `active` int NOT NULL DEFAULT '1' COMMENT '是否有效（0：失效；1：有效）',
  PRIMARY KEY (`id`),
  KEY `fk_tel_tenant_idx` (`tenant_id`),
  KEY `fk_tel_tclass_idx` (`cid`),
  KEY `fk_tel_tstudent_idx` (`sid`),
  KEY `fk_tel_texcercise_idx` (`eid`),
  CONSTRAINT `fk_tel_tclass` FOREIGN KEY (`cid`) REFERENCES `tnt_class` (`id`),
  CONSTRAINT `fk_tel_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_tel_texcercise` FOREIGN KEY (`eid`) REFERENCES `tnt_exercise` (`id`),
  CONSTRAINT `fk_tel_tstudent` FOREIGN KEY (`sid`) REFERENCES `tnt_student` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='练习情况表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_framework`
--

DROP TABLE IF EXISTS `tnt_framework`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_framework` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '理论框架ID',
  `name` varchar(255) NOT NULL COMMENT '理论框架名称（如：PMI人才三角等）',
  `description` text COMMENT '详细描述',
  `logo` varchar(255) DEFAULT NULL COMMENT 'Logo URL',
  `priority` int NOT NULL DEFAULT '1' COMMENT '展示顺序（从小到大）',
  `active` int NOT NULL DEFAULT '1' COMMENT '是否有效（0：失效；1：有效）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`),
  KEY `fk_tframework_tenant_idx` (`tenant_id`),
  CONSTRAINT `fk_tframework_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='理论框架表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_line`
--

DROP TABLE IF EXISTS `tnt_line`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_line` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '台词ID',
  `cueid` int NOT NULL COMMENT '剧本提示ID',
  `cid` int NOT NULL COMMENT '人物ID（发言人物）',
  `pv_topic` varchar(512) NOT NULL COMMENT '发言主题（提示词变量，后台用）',
  `pv_ability` text NOT NULL COMMENT '人物能力（提示词变量，后台用）',
  `pv_restriction` text NOT NULL COMMENT '人物限制（提示词变量，后台用）',
  `priority` int NOT NULL DEFAULT '1' COMMENT '展示顺序（从小到大）',
  `active` int NOT NULL DEFAULT '1' COMMENT '是否有效（0：失效；1：有效）',
  PRIMARY KEY (`id`),
  KEY `fk_tline_tenant_idx` (`tenant_id`),
  KEY `fk_tline_tcue_idx` (`cueid`),
  KEY `fk_tline_tcharacter_idx` (`cid`),
  CONSTRAINT `fk_tline_tcharacter` FOREIGN KEY (`cid`) REFERENCES `tnt_character` (`id`),
  CONSTRAINT `fk_tline_tcue` FOREIGN KEY (`cueid`) REFERENCES `tnt_cue` (`id`),
  CONSTRAINT `fk_tline_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='台词表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_module`
--

DROP TABLE IF EXISTS `tnt_module`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_module` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '理论模块ID',
  `fid` int NOT NULL COMMENT '理论框架ID',
  `name` varchar(255) NOT NULL COMMENT '理论模块名称',
  `description` text COMMENT '详细描述',
  `priority` int NOT NULL DEFAULT '1' COMMENT '展示顺序（从小到大）',
  `active` int NOT NULL DEFAULT '1' COMMENT '是否有效（0：失效；1：有效）',
  PRIMARY KEY (`id`),
  KEY `fk_tmodule_tenant_idx` (`tenant_id`),
  KEY `fk_tmodule_tframework_idx` (`fid`),
  CONSTRAINT `fk_tmodule_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_tmodule_tframework` FOREIGN KEY (`fid`) REFERENCES `tnt_framework` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='理论模块表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_plan`
--

DROP TABLE IF EXISTS `tnt_plan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_plan` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '计划ID',
  `name` varchar(255) NOT NULL COMMENT '名称',
  `pic` varchar(255) DEFAULT NULL COMMENT '图片URL',
  `description` text COMMENT '描述',
  `notes` varchar(512) DEFAULT NULL COMMENT '备注',
  `active` int NOT NULL DEFAULT '1' COMMENT '是否有效（0：失效；1：有效）',
  PRIMARY KEY (`id`),
  KEY `fk_tplan_tenant_idx` (`tenant_id`),
  CONSTRAINT `fk_tplan_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='计划表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_plan_exercise`
--

DROP TABLE IF EXISTS `tnt_plan_exercise`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_plan_exercise` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `pid` int NOT NULL COMMENT '计划ID',
  `eid` int NOT NULL COMMENT '练习ID',
  `depend` int NOT NULL DEFAULT '1' COMMENT '是否依赖前一个练习结束（0：不依赖；1：依赖）',
  `priority` int NOT NULL DEFAULT '1' COMMENT '展示顺序（从小到大）',
  PRIMARY KEY (`id`),
  KEY `fk_tpe_tenant_idx` (`tenant_id`),
  KEY `fk_tpe_tplan_idx` (`pid`),
  KEY `fk_tpe_texercise_idx` (`eid`),
  CONSTRAINT `fk_tpe_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_tpe_texercise` FOREIGN KEY (`eid`) REFERENCES `tnt_exercise` (`id`),
  CONSTRAINT `fk_tpe_tplan` FOREIGN KEY (`pid`) REFERENCES `tnt_plan` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='计划练习关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_question`
--

DROP TABLE IF EXISTS `tnt_question`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_question` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '问题ID',
  `title` varchar(255) NOT NULL COMMENT '标题',
  `bgtext` text COMMENT '背景文字',
  `bgvideo` varchar(255) DEFAULT NULL COMMENT '背景视频URL',
  `notes` varchar(512) DEFAULT NULL COMMENT '备注',
  `pv_skills` text COMMENT '点评能力（提示词变量，后台用）',
  `pv_rules` text COMMENT '点评细则（提示词变量，后台用）',
  `pv_formats` text COMMENT '点评格式（提示词变量，后台用）',
  `ctime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `active` int NOT NULL DEFAULT '1' COMMENT '是否有效（0：失效；1：有效）',
  PRIMARY KEY (`id`),
  KEY `fk_tquestion_tenant_idx` (`tenant_id`),
  CONSTRAINT `fk_tquestion_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='问题表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_question_guide`
--

DROP TABLE IF EXISTS `tnt_question_guide`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_question_guide` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '作答指南ID',
  `qid` int NOT NULL COMMENT '问题ID',
  `title` varchar(255) NOT NULL COMMENT '指南标题',
  `details` text NOT NULL COMMENT '指南详情',
  `priority` int NOT NULL DEFAULT '1' COMMENT '展示顺序（从小到大）',
  PRIMARY KEY (`id`),
  KEY `fk_tqg_tquestion_idx` (`qid`),
  KEY `fk_tqg_tenant_idx` (`tenant_id`),
  CONSTRAINT `fk_tqg_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_tqg_tquestion` FOREIGN KEY (`qid`) REFERENCES `tnt_question` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='问题作答指南表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_question_module`
--

DROP TABLE IF EXISTS `tnt_question_module`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_question_module` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `qid` int NOT NULL COMMENT '问题ID',
  `mid` int NOT NULL COMMENT '理论模块ID',
  PRIMARY KEY (`id`),
  KEY `fk_tqm_tquestion_idx` (`qid`),
  KEY `fk_tqm_tmodule_idx` (`mid`),
  KEY `fk_tqm_tenant_idx` (`tenant_id`),
  CONSTRAINT `fk_tqm_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_tqm_tmodule` FOREIGN KEY (`mid`) REFERENCES `tnt_module` (`id`),
  CONSTRAINT `fk_tqm_tquestion` FOREIGN KEY (`qid`) REFERENCES `tnt_question` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='问题-理论模块关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_question_subject`
--

DROP TABLE IF EXISTS `tnt_question_subject`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_question_subject` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `qid` int NOT NULL COMMENT '问题ID',
  `sid` int NOT NULL COMMENT '主题ID',
  PRIMARY KEY (`id`),
  KEY `fk_tqs_tquestion_idx` (`qid`),
  KEY `fk_tqs_tsubject_idx` (`sid`),
  KEY `fk_tqs_tenant_idx` (`tenant_id`),
  CONSTRAINT `fk_tqs_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_tqs_tquestion` FOREIGN KEY (`qid`) REFERENCES `tnt_question` (`id`),
  CONSTRAINT `fk_tqs_tsubject` FOREIGN KEY (`sid`) REFERENCES `tnt_subject` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='问题-主题关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_scene`
--

DROP TABLE IF EXISTS `tnt_scene`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_scene` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '场景ID',
  `eid` int NOT NULL COMMENT '练习ID',
  `pv_scripts` text NOT NULL COMMENT '剧情脚本（提示词变量，后台用）',
  PRIMARY KEY (`id`),
  KEY `fk_tscene_tenant_idx` (`tenant_id`),
  CONSTRAINT `fk_tscene_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='场景表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_scene_character`
--

DROP TABLE IF EXISTS `tnt_scene_character`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_scene_character` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `sid` int NOT NULL COMMENT '场景ID',
  `cid` int NOT NULL COMMENT '人物ID',
  `played` int NOT NULL DEFAULT '0' COMMENT '是否为学员扮演（0：否；1：是）',
  `priority` int NOT NULL DEFAULT '1' COMMENT '展示顺序（从小到大）',
  PRIMARY KEY (`id`),
  KEY `fk_tsc_tenant_idx` (`tenant_id`),
  KEY `fk_tsc_scene_idx` (`sid`),
  KEY `fk_tsc_tcharacter_idx` (`cid`),
  CONSTRAINT `fk_tsc_tcharacter` FOREIGN KEY (`cid`) REFERENCES `tnt_character` (`id`),
  CONSTRAINT `fk_tsc_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_tsc_tscene` FOREIGN KEY (`sid`) REFERENCES `tnt_scene` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='场景人物关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_scene_guide`
--

DROP TABLE IF EXISTS `tnt_scene_guide`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_scene_guide` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '指南ID',
  `sid` int NOT NULL COMMENT '场景ID',
  `title` varchar(255) NOT NULL COMMENT '指南标题',
  `details` text NOT NULL COMMENT '指南详情',
  `priority` int NOT NULL DEFAULT '1' COMMENT '展示顺序（从小到大）',
  PRIMARY KEY (`id`),
  KEY `fk_tsg_tenant_idx` (`tenant_id`),
  KEY `fk_tsg_tscene_idx` (`sid`),
  CONSTRAINT `fk_tsg_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_tsg_tscene` FOREIGN KEY (`sid`) REFERENCES `tnt_scene` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='场景练习指南表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_scene_speech`
--

DROP TABLE IF EXISTS `tnt_scene_speech`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_scene_speech` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '场景练习情况ID',
  `elid` int NOT NULL COMMENT '练习情况ID',
  `cid` int NOT NULL COMMENT '角色ID',
  `played` int NOT NULL DEFAULT '0' COMMENT '是否是学员扮演（0：否；1：是）',
  `content` text NOT NULL COMMENT '发言内容',
  `to_cids` varchar(255) DEFAULT NULL COMMENT '@列表（人物ID列表，用逗号分隔，可为空）',
  `ctime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发言时间',
  PRIMARY KEY (`id`),
  KEY `fk_tss_tenant_idx` (`tenant_id`),
  KEY `fk_tss_tel_idx` (`elid`),
  KEY `fk_tss_tcharacter_idx` (`cid`),
  CONSTRAINT `fk_tss_tcharacter` FOREIGN KEY (`cid`) REFERENCES `tnt_character` (`id`),
  CONSTRAINT `fk_tss_tel` FOREIGN KEY (`elid`) REFERENCES `tnt_exercise_log` (`id`),
  CONSTRAINT `fk_tss_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='场景练习情况表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_student`
--

DROP TABLE IF EXISTS `tnt_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_student` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '学员ID',
  `uid` int NOT NULL COMMENT '用户ID',
  `name` varchar(255) NOT NULL COMMENT '姓名',
  `gender` int NOT NULL DEFAULT '0' COMMENT '性别（0：未知；1：男；2：女；）',
  `notes` varchar(512) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `fk_tstudent_tenant_idx` (`tenant_id`),
  KEY `fk_tstudent_user_idx` (`uid`),
  CONSTRAINT `fk_tstudent_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_tstudent_user` FOREIGN KEY (`uid`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='学员表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_subject`
--

DROP TABLE IF EXISTS `tnt_subject`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_subject` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主题ID',
  `name` varchar(255) NOT NULL COMMENT '主题名称（如：项目管理、领导力、沟通技巧等）',
  PRIMARY KEY (`id`),
  KEY `fk_tsubject_tenant_idx` (`tenant_id`),
  CONSTRAINT `fk_tsubject_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='主题表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_teacher`
--

DROP TABLE IF EXISTS `tnt_teacher`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_teacher` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '老师ID',
  `name` varchar(255) NOT NULL COMMENT '姓名',
  `gender` int NOT NULL DEFAULT '0' COMMENT '性别（0：未知；1：男；2：女；）',
  `avatar` varchar(255) NOT NULL COMMENT '头像URL',
  `intro` text COMMENT '简介',
  `notes` varchar(512) DEFAULT NULL COMMENT '备注',
  `ctime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `fk_tteacher_tenant_idx` (`tenant_id`),
  CONSTRAINT `fk_tteacher_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='老师表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_unit`
--

DROP TABLE IF EXISTS `tnt_unit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_unit` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '单元模块ID',
  `wid` int NOT NULL COMMENT '作业单ID',
  `name` varchar(255) NOT NULL COMMENT '名称',
  `bgtext` text COMMENT '背景文字',
  `bgvideo` varchar(255) DEFAULT NULL COMMENT '背景视频URL',
  `priority` int NOT NULL DEFAULT '1' COMMENT '展示顺序（从小到大）',
  PRIMARY KEY (`id`),
  KEY `fk_tunit_tenant_idx` (`tenant_id`),
  KEY `fk_tunit_tworksheet_idx` (`wid`),
  CONSTRAINT `fk_tunit_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_tunit_tworksheet` FOREIGN KEY (`wid`) REFERENCES `tnt_worksheet` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='单元模块表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_worksheet`
--

DROP TABLE IF EXISTS `tnt_worksheet`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_worksheet` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '作业单ID',
  `eid` int NOT NULL COMMENT '练习ID',
  PRIMARY KEY (`id`),
  KEY `fk_tworksheet_tenant_idx` (`tenant_id`),
  KEY `fk_tworksheet_texercise_idx` (`eid`),
  CONSTRAINT `fk_tworksheet_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_tworksheet_texercise` FOREIGN KEY (`eid`) REFERENCES `tnt_exercise` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='作业单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_worksheet_answer`
--

DROP TABLE IF EXISTS `tnt_worksheet_answer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_worksheet_answer` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '作业单作答ID',
  `elid` int NOT NULL COMMENT '练习情况ID',
  `qid` int NOT NULL COMMENT '问题ID',
  `draft` text NOT NULL COMMENT '草稿',
  `answer` text NOT NULL COMMENT '已提交作答',
  `comment` text NOT NULL COMMENT 'AI点评内容',
  `stime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
  `utime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '草稿更新时间',
  PRIMARY KEY (`id`),
  KEY `fk_twa_tenant_idx` (`tenant_id`),
  KEY `fk_twa_tel_idx` (`elid`),
  KEY `fk_twa_tquestion_idx` (`qid`),
  CONSTRAINT `fk_twa_tel` FOREIGN KEY (`elid`) REFERENCES `tnt_exercise_log` (`id`),
  CONSTRAINT `fk_twa_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_twa_tquestion` FOREIGN KEY (`qid`) REFERENCES `tnt_question` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='作业单作答表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tnt_worksheet_org`
--

DROP TABLE IF EXISTS `tnt_worksheet_org`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tnt_worksheet_org` (
  `tenant_id` int NOT NULL COMMENT '租户ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `wid` int NOT NULL COMMENT '作业单ID',
  `uid` int NOT NULL COMMENT '单元ID',
  `qid` int NOT NULL COMMENT '问题ID',
  `priority` int NOT NULL DEFAULT '1' COMMENT '展示顺序（从小到大）',
  PRIMARY KEY (`id`),
  KEY `fk_two_tworksheet_idx` (`wid`),
  KEY `fk_two_tunit_idx` (`uid`),
  KEY `fk_two_tquestion_idx` (`qid`),
  KEY `fk_two_tenant_idx` (`tenant_id`),
  CONSTRAINT `fk_two_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `sys_tenant` (`id`),
  CONSTRAINT `fk_two_tquestion` FOREIGN KEY (`qid`) REFERENCES `tnt_question` (`id`),
  CONSTRAINT `fk_two_tunit` FOREIGN KEY (`uid`) REFERENCES `tnt_unit` (`id`),
  CONSTRAINT `fk_two_tworksheet` FOREIGN KEY (`wid`) REFERENCES `tnt_worksheet` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='作业单组织关系表';
/*!40101 SET character_set_client = @saved_cs_client */;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-05 11:06:34
